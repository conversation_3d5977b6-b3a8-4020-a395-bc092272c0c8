{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "assets/index-BMw_JMhz.b24jitss8o.js", "AssetFile": "assets/index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b24jitss8o"}, {"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}, {"Name": "label", "Value": "assets/index-BMw_JMhz.js"}]}, {"Route": "assets/index-BMw_JMhz.js", "AssetFile": "assets/index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-H3RB1j-F.b510bptf2a.css", "AssetFile": "assets/index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b510bptf2a"}, {"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}, {"Name": "label", "Value": "assets/index-H3RB1j-F.css"}]}, {"Route": "assets/index-H3RB1j-F.css", "AssetFile": "assets/index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I="}]}, {"Route": "index.j2eqvkfzk9.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j2eqvkfzk9"}, {"Name": "integrity", "Value": "sha256-u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I="}, {"Name": "label", "Value": "index.html"}]}]}