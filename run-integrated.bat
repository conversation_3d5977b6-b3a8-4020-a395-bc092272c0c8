@echo off
echo Starting Vehicle Activator System (Integrated Mode)...

echo.
echo ========================================
echo Building React Frontend...
echo ========================================
cd vehicle-activator-frontend
call npm install
if %errorlevel% neq 0 (
    echo Failed to install npm packages
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo Failed to build React frontend
    pause
    exit /b 1
)

echo.
echo ========================================
echo Copying Frontend to API wwwroot...
echo ========================================
cd ..\VehicleActivatorAPI
if not exist "wwwroot" mkdir "wwwroot"
xcopy "..\vehicle-activator-frontend\dist\*" "wwwroot\" /E /Y
if %errorlevel% neq 0 (
    echo Failed to copy frontend files
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting Integrated Application...
echo ========================================
echo.
echo The application will be available at:
echo   - HTTPS: https://localhost:7000/
echo   - HTTP:  http://localhost:5000/
echo.
echo Both frontend and API are served from the same URL:
echo   - Web Interface: https://localhost:7000/
echo   - API Endpoints: https://localhost:7000/api/
echo   - Swagger UI: https://localhost:7000/swagger
echo.

call dotnet run

echo.
echo Press any key to exit...
pause > nul
