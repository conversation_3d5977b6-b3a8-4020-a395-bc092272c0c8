@echo off
echo Building Vehicle Activator System for Production...

echo.
echo ========================================
echo Building React Frontend for Production...
echo ========================================
cd vehicle-activator-frontend
call npm install
if %errorlevel% neq 0 (
    echo Failed to install npm packages
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo Failed to build React frontend
    pause
    exit /b 1
)

echo.
echo ========================================
echo Publishing .NET Core API...
echo ========================================
cd ..\VehicleActivatorAPI
call dotnet publish -c Release -o ..\publish\api
if %errorlevel% neq 0 (
    echo Failed to publish .NET API
    pause
    exit /b 1
)

echo.
echo ========================================
echo Copying Frontend Build to API...
echo ========================================
cd ..
if not exist "publish\api\wwwroot" mkdir "publish\api\wwwroot"
xcopy "vehicle-activator-frontend\dist\*" "publish\api\wwwroot\" /E /Y
if %errorlevel% neq 0 (
    echo Failed to copy frontend files
    pause
    exit /b 1
)

echo.
echo ========================================
echo Creating Production Configuration...
echo ========================================
echo Creating production appsettings...
(
echo {
echo   "ConnectionStrings": {
echo     "DefaultConnection": "Server=.;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;TrustServerCertificate=true;"
echo   },
echo   "Logging": {
echo     "LogLevel": {
echo       "Default": "Information",
echo       "Microsoft.AspNetCore": "Warning"
echo     }
echo   },
echo   "AllowedHosts": "*",
echo   "Urls": "https://localhost:5001;http://localhost:5000"
echo }
) > "publish\api\appsettings.Production.json"

echo.
echo ========================================
echo Creating Run Script...
echo ========================================
(
echo @echo off
echo echo Starting Vehicle Activator System...
echo echo API will be available at: https://localhost:5001
echo echo Web Interface will be available at: https://localhost:5001
echo echo.
echo VehicleActivatorAPI.exe --environment=Production
echo pause
) > "publish\run-production.bat"

echo.
echo ========================================
echo Production Build Complete!
echo ========================================
echo.
echo Files are located in: .\publish\
echo.
echo To run in production:
echo 1. Update the connection string in publish\api\appsettings.Production.json
echo 2. Run publish\run-production.bat
echo.
echo Press any key to exit...
pause > nul
