@echo off
echo Building Vehicle Activator System for Production (Single URL)...

echo.
echo ========================================
echo Building React Frontend for Production...
echo ========================================
cd vehicle-activator-frontend
call npm install
if %errorlevel% neq 0 (
    echo Failed to install npm packages
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo Failed to build React frontend
    pause
    exit /b 1
)

echo.
echo ========================================
echo Copying Frontend Build to API wwwroot...
echo ========================================
cd ..\VehicleActivatorAPI
if not exist "wwwroot" mkdir "wwwroot"
xcopy "..\vehicle-activator-frontend\dist\*" "wwwroot\" /E /Y
if %errorlevel% neq 0 (
    echo Failed to copy frontend files
    pause
    exit /b 1
)

echo.
echo ========================================
echo Publishing Integrated .NET Core Application...
echo ========================================
call dotnet publish -c Release -o ..\publish\integrated
if %errorlevel% neq 0 (
    echo Failed to publish integrated application
    pause
    exit /b 1
)

echo.
echo ========================================
echo Creating Production Configuration...
echo ========================================
cd ..
echo Creating production appsettings...
(
echo {
echo   "ConnectionStrings": {
echo     "DefaultConnection": "Server=.;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;TrustServerCertificate=true;"
echo   },
echo   "Logging": {
echo     "LogLevel": {
echo       "Default": "Information",
echo       "Microsoft.AspNetCore": "Warning"
echo     }
echo   },
echo   "AllowedHosts": "*",
echo   "Urls": "https://localhost:5001;http://localhost:5000"
echo }
) > "publish\integrated\appsettings.Production.json"

echo.
echo ========================================
echo Creating Run Script...
echo ========================================
(
echo @echo off
echo echo Starting Vehicle Activator System ^(Integrated^)...
echo echo.
echo echo The application will be available at:
echo echo   - HTTPS: https://localhost:5001
echo echo   - HTTP:  http://localhost:5000
echo echo.
echo echo Both API and Web Interface are served from the same URL:
echo echo   - Web Interface: https://localhost:5001/
echo echo   - API Endpoints: https://localhost:5001/api/
echo echo   - Swagger UI: https://localhost:5001/swagger ^(Development only^)
echo echo.
echo VehicleActivatorAPI.exe --environment=Production
echo pause
) > "publish\integrated\run-production.bat"

echo.
echo ========================================
echo Integrated Production Build Complete!
echo ========================================
echo.
echo Files are located in: .\publish\integrated\
echo.
echo SINGLE URL DEPLOYMENT:
echo - Both frontend and API are served from the same URL
echo - Frontend: https://localhost:5001/
echo - API: https://localhost:5001/api/
echo.
echo To run in production:
echo 1. Update the connection string in publish\integrated\appsettings.Production.json
echo 2. Run publish\integrated\run-production.bat
echo.
echo Press any key to exit...
pause > nul
