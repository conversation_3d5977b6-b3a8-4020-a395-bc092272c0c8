@echo off
echo Testing Vehicle Activator API...

echo.
echo ========================================
echo Starting API Server...
echo ========================================
cd VehicleActivatorAPI
start "API Server" cmd /k "dotnet run"

echo Waiting for API to start...
timeout /t 10 /nobreak > nul

echo.
echo ========================================
echo Testing API Endpoints...
echo ========================================

echo Testing GET /api/vehicles...
curl -k -X GET "https://localhost:7000/api/vehicles" -H "accept: application/json"

echo.
echo.
echo Testing GET /api/vehicles with filter...
curl -k -X GET "https://localhost:7000/api/vehicles?soXeFilter=test" -H "accept: application/json"

echo.
echo.
echo Testing GET /api/vehicles/activated...
curl -k -X GET "https://localhost:7000/api/vehicles/activated" -H "accept: application/json"

echo.
echo.
echo ========================================
echo API Test Complete
echo ========================================
echo Check the responses above for any errors.
echo The API server is still running in the background.
echo.
echo Press any key to exit...
pause > nul
