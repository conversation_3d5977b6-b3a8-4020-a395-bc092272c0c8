{"version": 3, "targets": {"net8.0": {"Azure.Core/1.25.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net5.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.7.0": {"type": "package", "dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.39.0", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.5.0]", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "compile": {"ref/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.0"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.38.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.4.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Mono.TextTemplating/2.2.1": {"type": "package", "dependencies": {"System.CodeDom": "4.4.0"}, "compile": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections.Immutable/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Convention/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Hosting/6.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Runtime/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.TypedParts/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.Pipelines/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Metadata/6.0.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "6.0.0"}, "compile": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/6.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Azure.Core/1.25.0": {"sha512": "X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "type": "package", "path": "azure.core/1.25.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.25.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net5.0/Azure.Core.dll", "lib/net5.0/Azure.Core.xml", "lib/netcoreapp2.1/Azure.Core.dll", "lib/netcoreapp2.1/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.7.0": {"sha512": "eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "type": "package", "path": "azure.identity/1.7.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.7.0.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"sha512": "T4mwMvPSOYAp+KeQ4xO8H2rxpiOMJ9W/7yBBkUTMp96AHtGlPN4s7hbax2tM61LxTY775JKL4fiv5grn41EHXw==", "type": "package", "path": "microsoft.aspnetcore.openapi/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net8.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"sha512": "j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "build/config/analysislevel_2_9_8_all.editorconfig", "build/config/analysislevel_2_9_8_default.editorconfig", "build/config/analysislevel_2_9_8_minimum.editorconfig", "build/config/analysislevel_2_9_8_none.editorconfig", "build/config/analysislevel_2_9_8_recommended.editorconfig", "build/config/analysislevel_3_3_all.editorconfig", "build/config/analysislevel_3_3_default.editorconfig", "build/config/analysislevel_3_3_minimum.editorconfig", "build/config/analysislevel_3_3_none.editorconfig", "build/config/analysislevel_3_3_recommended.editorconfig", "build/config/analysislevel_3_all.editorconfig", "build/config/analysislevel_3_default.editorconfig", "build/config/analysislevel_3_minimum.editorconfig", "build/config/analysislevel_3_none.editorconfig", "build/config/analysislevel_3_recommended.editorconfig", "build/config/analysislevelcorrectness_2_9_8_all.editorconfig", "build/config/analysislevelcorrectness_2_9_8_default.editorconfig", "build/config/analysislevelcorrectness_2_9_8_minimum.editorconfig", "build/config/analysislevelcorrectness_2_9_8_none.editorconfig", "build/config/analysislevelcorrectness_2_9_8_recommended.editorconfig", "build/config/analysislevelcorrectness_3_3_all.editorconfig", "build/config/analysislevelcorrectness_3_3_default.editorconfig", "build/config/analysislevelcorrectness_3_3_minimum.editorconfig", "build/config/analysislevelcorrectness_3_3_none.editorconfig", "build/config/analysislevelcorrectness_3_3_recommended.editorconfig", "build/config/analysislevelcorrectness_3_all.editorconfig", "build/config/analysislevelcorrectness_3_default.editorconfig", "build/config/analysislevelcorrectness_3_minimum.editorconfig", "build/config/analysislevelcorrectness_3_none.editorconfig", "build/config/analysislevelcorrectness_3_recommended.editorconfig", "build/config/analysislevellibrary_2_9_8_all.editorconfig", "build/config/analysislevellibrary_2_9_8_default.editorconfig", "build/config/analysislevellibrary_2_9_8_minimum.editorconfig", "build/config/analysislevellibrary_2_9_8_none.editorconfig", "build/config/analysislevellibrary_2_9_8_recommended.editorconfig", "build/config/analysislevellibrary_3_3_all.editorconfig", "build/config/analysislevellibrary_3_3_default.editorconfig", "build/config/analysislevellibrary_3_3_minimum.editorconfig", "build/config/analysislevellibrary_3_3_none.editorconfig", "build/config/analysislevellibrary_3_3_recommended.editorconfig", "build/config/analysislevellibrary_3_all.editorconfig", "build/config/analysislevellibrary_3_default.editorconfig", "build/config/analysislevellibrary_3_minimum.editorconfig", "build/config/analysislevellibrary_3_none.editorconfig", "build/config/analysislevellibrary_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.5.0": {"sha512": "lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "type": "package", "path": "microsoft.codeanalysis.common/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.5.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"sha512": "cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"sha512": "h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"sha512": "l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/5.1.1": {"sha512": "MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "type": "package", "path": "microsoft.data.sqlclient/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.pdb", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.pdb", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.5.1.1.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.pdb", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.pdb", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"sha512": "jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.EntityFrameworkCore/8.0.0": {"sha512": "SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "type": "package", "path": "microsoft.entityframeworkcore/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"sha512": "VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"sha512": "ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"sha512": "94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "type": "package", "path": "microsoft.entityframeworkcore.design/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net8.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"sha512": "fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "type": "package", "path": "microsoft.entityframeworkcore.relational/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"sha512": "GeOmafQn64HyQtYcI/Omv/D/YVHd1zEkWbP3zNQu4oC+usE9K0qOp0R8KgWWFEf8BU4tXuYbok40W0SjfbaK/A==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"sha512": "zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "type": "package", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net8.0/_._", "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net461/any/ef.exe", "tools/net461/win-arm64/ef.exe", "tools/net461/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.0": {"sha512": "NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.47.2": {"sha512": "SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "type": "package", "path": "microsoft.identity.client/4.47.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/net5.0-windows10.0.17763/Microsoft.Identity.Client.dll", "lib/net5.0-windows10.0.17763/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "lib/uap10.0.17763/Microsoft.Identity.Client.dll", "lib/uap10.0.17763/Microsoft.Identity.Client.pri", "lib/uap10.0.17763/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "lib/xamarinmac20/Microsoft.Identity.Client.dll", "lib/xamarinmac20/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.47.2.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"sha512": "zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "type": "package", "path": "microsoft.identity.client.extensions.msal/2.19.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net45/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"sha512": "X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"sha512": "XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.24.0": {"sha512": "qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "type": "package", "path": "microsoft.identitymodel.logging/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.24.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.24.0": {"sha512": "+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "type": "package", "path": "microsoft.identitymodel.protocols/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"sha512": "a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.24.0": {"sha512": "ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "type": "package", "path": "microsoft.identitymodel.tokens/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.OpenApi/1.4.3": {"sha512": "rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "type": "package", "path": "microsoft.openapi/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.4.3.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Mono.TextTemplating/2.2.1": {"sha512": "KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "type": "package", "path": "mono.texttemplating/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/Mono.TextTemplating.dll", "lib/netstandard2.0/Mono.TextTemplating.dll", "mono.texttemplating.2.2.1.nupkg.sha512", "mono.texttemplating.nuspec"]}, "Swashbuckle.AspNetCore/6.4.0": {"sha512": "eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "type": "package", "path": "swashbuckle.aspnetcore/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"sha512": "nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"sha512": "lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"sha512": "1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/6.0.0": {"sha512": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "type": "package", "path": "system.collections.immutable/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.6.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/6.0.0": {"sha512": "d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "type": "package", "path": "system.composition/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.targets", "buildTransitive/netcoreapp3.1/_._", "system.composition.6.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/6.0.0": {"sha512": "WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "type": "package", "path": "system.composition.attributedmodel/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.AttributedModel.dll", "lib/net461/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.6.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/6.0.0": {"sha512": "XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "type": "package", "path": "system.composition.convention/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Convention.dll", "lib/net461/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.6.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/6.0.0": {"sha512": "w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "type": "package", "path": "system.composition.hosting/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Hosting.dll", "lib/net461/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.6.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/6.0.0": {"sha512": "qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "type": "package", "path": "system.composition.runtime/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Runtime.dll", "lib/net461/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.6.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/6.0.0": {"sha512": "iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "type": "package", "path": "system.composition.typedparts/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.TypedParts.dll", "lib/net461/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.6.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/6.0.1": {"sha512": "jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "type": "package", "path": "system.configuration.configurationmanager/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.0": {"sha512": "frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/5.0.0": {"sha512": "MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "type": "package", "path": "system.formats.asn1/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.5.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"sha512": "Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.24.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.Pipelines/6.0.3": {"sha512": "ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "type": "package", "path": "system.io.pipelines/6.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/netcoreapp3.1/System.IO.Pipelines.dll", "lib/netcoreapp3.1/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.6.0.3.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/6.0.1": {"sha512": "III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "type": "package", "path": "system.reflection.metadata/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/net6.0/System.Reflection.Metadata.dll", "lib/net6.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.6.0.1.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/6.0.0": {"sha512": "E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "type": "package", "path": "system.runtime.caching/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/netcoreapp3.1/System.Runtime.Caching.dll", "lib/netcoreapp3.1/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.dll", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.xml", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.6.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/6.0.0": {"sha512": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "type": "package", "path": "system.threading.channels/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Threading.Channels.dll", "lib/net461/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/netcoreapp3.1/System.Threading.Channels.dll", "lib/netcoreapp3.1/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.6.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.AspNetCore.OpenApi >= 8.0.0", "Microsoft.EntityFrameworkCore.Design >= 8.0.0", "Microsoft.EntityFrameworkCore.SqlServer >= 8.0.0", "Microsoft.EntityFrameworkCore.Tools >= 8.0.0", "Swashbuckle.AspNetCore >= 6.4.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\VehicleActivatorAPI.csproj", "projectName": "VehicleActivatorAPI", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\VehicleActivatorAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}