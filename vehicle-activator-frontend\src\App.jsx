import React, { useState } from 'react'
import VehicleList from './components/VehicleList'
import MachineLogList from './components/MachineLogList'

function App() {
  const [activeTab, setActiveTab] = useState('vehicles')

  return (
    <div className="App">
      <div className="container">
        <div className="nav-tabs">
          <button
            className={`nav-tab ${activeTab === 'vehicles' ? 'active' : ''}`}
            onClick={() => setActiveTab('vehicles')}
          >
            Vehicle Activator
          </button>
          <button
            className={`nav-tab ${activeTab === 'machinelogs' ? 'active' : ''}`}
            onClick={() => setActiveTab('machinelogs')}
          >
            Machine Log Activator
          </button>
        </div>
      </div>

      {activeTab === 'vehicles' && <VehicleList />}
      {activeTab === 'machinelogs' && <MachineLogList />}
    </div>
  )
}

export default App
