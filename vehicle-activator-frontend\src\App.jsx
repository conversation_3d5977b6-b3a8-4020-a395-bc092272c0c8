import React, { useState } from 'react'
import VehicleList from './components/VehicleList'
import MachineLogList from './components/MachineLogList'
import './App.css'

function App() {
  const [activeTab, setActiveTab] = useState('vehicles')

  return (
    <div className="app-container">
      <header className="app-header">
        <h1 className="app-title">Vehicle & Machine Log Activator</h1>
      </header>

      {/* Tab Navigation */}
      <nav className="tab-navigation">
        <button
          onClick={() => setActiveTab('vehicles')}
          className={`tab-button ${activeTab === 'vehicles' ? 'active' : ''}`}
        >
          🚗 Vehicle Activator
        </button>
        <button
          onClick={() => setActiveTab('machinelogs')}
          className={`tab-button ${activeTab === 'machinelogs' ? 'active' : ''}`}
        >
          🔧 Machine Log Activator
        </button>
      </nav>

      {/* Tab Content */}
      <main className="tab-content">
        {activeTab === 'vehicles' && <VehicleList />}
        {activeTab === 'machinelogs' && <MachineLogList />}
      </main>
    </div>
  )
}

export default App
