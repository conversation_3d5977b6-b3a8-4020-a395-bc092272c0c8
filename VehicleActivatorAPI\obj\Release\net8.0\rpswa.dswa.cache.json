{"GlobalPropertiesHash": "JXSxunYq4mSTA+3MPVsBkmq2RWyLlHLxbffaZT5XrIw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["AYzx14NcHTcyKMKmp6N6FHnzqkdUu+xcQFgYIz3WGvA=", "W82oTJhmNgmqRqDKgG25sfb/VwYj/4MQ5uZRKpnVc/k=", "9c71IbnamnvXc/e2qkb0Ez69KXeFBGYvoGxyB9Bezpw=", "SHLylOPIVhiBC5Z4QchdmXwkTyZxclXAwyurX5ZZH9g=", "oogFseg+XQAj4CV6r/JPH7o+KUlM5YvlbovgSPdfdHs=", "T2pI4YrtBLa8UjM19ntmdZFd1uxvSFyHDqb36o7cm/w=", "xI0emswQpDrE4E6Qo77FSELtEwZlclBL+oMcmS6skk8="], "CachedAssets": {"AYzx14NcHTcyKMKmp6N6FHnzqkdUu+xcQFgYIz3WGvA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-BMw_JMhz#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b24jitss8o", "Integrity": "U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-BMw_JMhz.js", "FileLength": 180965, "LastWriteTime": "2025-06-25T06:48:51.6783368+00:00"}, "W82oTJhmNgmqRqDKgG25sfb/VwYj/4MQ5uZRKpnVc/k=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-H3RB1j-F#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b510bptf2a", "Integrity": "z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-H3RB1j-F.css", "FileLength": 1499, "LastWriteTime": "2025-06-25T06:48:51.6783368+00:00"}, "9c71IbnamnvXc/e2qkb0Ez69KXeFBGYvoGxyB9Bezpw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j2eqvkfzk9", "Integrity": "u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 464, "LastWriteTime": "2025-06-25T06:48:51.6783368+00:00"}}, "CachedCopyCandidates": {}}