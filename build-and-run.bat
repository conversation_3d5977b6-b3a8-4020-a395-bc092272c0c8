@echo off
echo Building and running Vehicle Activator System...

echo.
echo ========================================
echo Building .NET Core API...
echo ========================================
cd VehicleActivatorAPI
call dotnet restore
if %errorlevel% neq 0 (
    echo Failed to restore .NET packages
    pause
    exit /b 1
)

call dotnet build
if %errorlevel% neq 0 (
    echo Failed to build .NET API
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installing React dependencies...
echo ========================================
cd ..\vehicle-activator-frontend
call npm install
if %errorlevel% neq 0 (
    echo Failed to install npm packages
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting applications...
echo ========================================
echo Starting .NET API on https://localhost:7000
start "API Server" cmd /k "cd ..\VehicleActivatorAPI && dotnet run"

timeout /t 5 /nobreak > nul

echo Starting React frontend on http://localhost:3000
start "React Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo Applications are starting...
echo ========================================
echo API: https://localhost:7000/swagger
echo Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause > nul
