@echo off
echo Building and running Vehicle Activator System...

echo.
echo ========================================
echo Building .NET Core API...
echo ========================================
cd VehicleActivatorAPI
call dotnet restore
if %errorlevel% neq 0 (
    echo Failed to restore .NET packages
    pause
    exit /b 1
)

call dotnet build
if %errorlevel% neq 0 (
    echo Failed to build .NET API
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installing React dependencies...
echo ========================================
cd ..\vehicle-activator-frontend
call npm install
if %errorlevel% neq 0 (
    echo Failed to install npm packages
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting applications...
echo ========================================
echo Starting React frontend development server on http://localhost:3000
start "React Frontend" cmd /k "npm run dev"

timeout /t 3 /nobreak > nul

echo Starting .NET API with SPA integration on https://localhost:7000
start "API Server with SPA" cmd /k "cd ..\VehicleActivatorAPI && dotnet run"

echo.
echo ========================================
echo Applications are starting...
echo ========================================
echo.
echo DEVELOPMENT MODE:
echo - React Dev Server: http://localhost:3000 (with hot reload)
echo - .NET API: https://localhost:7000/api/
echo - Swagger UI: https://localhost:7000/swagger
echo - Integrated SPA: https://localhost:7000/ (proxies to React dev server)
echo.
echo RECOMMENDED: Use https://localhost:7000/ for testing the integrated experience
echo.
echo Press any key to exit...
pause > nul
