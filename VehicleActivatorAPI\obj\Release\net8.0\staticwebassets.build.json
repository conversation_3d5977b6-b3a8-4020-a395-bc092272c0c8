{"Version": 1, "Hash": "TXOgkNZzuKhe4YmOYHer85QeIS7Mic0cT7vHseUHtBA=", "Source": "VehicleActivatorAPI", "BasePath": "_content/VehicleActivatorAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "VehicleActivatorAPI\\wwwroot", "Source": "VehicleActivatorAPI", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-BMw_JMhz#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b24jitss8o", "Integrity": "U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-BMw_JMhz.js", "FileLength": 180965, "LastWriteTime": "2025-06-25T06:48:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CJChw8ry.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-CJChw8ry#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3hojn27uz5", "Integrity": "WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-CJChw8ry.js", "FileLength": 182241, "LastWriteTime": "2025-06-25T07:12:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-COJ0l26b.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-COJ0l26b#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3fgy8k75fl", "Integrity": "nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-COJ0l26b.css", "FileLength": 4666, "LastWriteTime": "2025-06-25T07:56:33+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CpdsusLQ.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-CpdsusLQ#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t1hsu86971", "Integrity": "rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-CpdsusLQ.css", "FileLength": 3109, "LastWriteTime": "2025-06-25T07:12:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-H3RB1j-F#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b510bptf2a", "Integrity": "z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-H3RB1j-F.css", "FileLength": 1499, "LastWriteTime": "2025-06-25T06:48:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-QbAIgLC0.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-QbAIgLC0#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ikfqtwtpkd", "Integrity": "Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-QbAIgLC0.js", "FileLength": 186307, "LastWriteTime": "2025-06-25T07:56:33+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1hjrecezz3", "Integrity": "q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 464, "LastWriteTime": "2025-06-25T07:56:33+00:00"}], "Endpoints": [{"Route": "assets/index-BMw_JMhz.b24jitss8o.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b24jitss8o"}, {"Name": "label", "Value": "assets/index-BMw_JMhz.js"}, {"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-BMw_JMhz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-CJChw8ry.3hojn27uz5.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CJChw8ry.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "182241"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3hojn27uz5"}, {"Name": "label", "Value": "assets/index-CJChw8ry.js"}, {"Name": "integrity", "Value": "sha256-WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs="}]}, {"Route": "assets/index-CJChw8ry.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CJChw8ry.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "182241"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs="}]}, {"Route": "assets/index-COJ0l26b.3fgy8k75fl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-COJ0l26b.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4666"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fgy8k75fl"}, {"Name": "label", "Value": "assets/index-COJ0l26b.css"}, {"Name": "integrity", "Value": "sha256-nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I="}]}, {"Route": "assets/index-COJ0l26b.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-COJ0l26b.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4666"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I="}]}, {"Route": "assets/index-CpdsusLQ.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CpdsusLQ.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA="}]}, {"Route": "assets/index-CpdsusLQ.t1hsu86971.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CpdsusLQ.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1hsu86971"}, {"Name": "label", "Value": "assets/index-CpdsusLQ.css"}, {"Name": "integrity", "Value": "sha256-rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA="}]}, {"Route": "assets/index-H3RB1j-F.b510bptf2a.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b510bptf2a"}, {"Name": "label", "Value": "assets/index-H3RB1j-F.css"}, {"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "assets/index-H3RB1j-F.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "assets/index-QbAIgLC0.ikfqtwtpkd.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-QbAIgLC0.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186307"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ikfqtwtpkd"}, {"Name": "label", "Value": "assets/index-QbAIgLC0.js"}, {"Name": "integrity", "Value": "sha256-Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM="}]}, {"Route": "assets/index-QbAIgLC0.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-QbAIgLC0.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "186307"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM="}]}, {"Route": "index.1hjrecezz3.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1hjrecezz3"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ="}]}]}