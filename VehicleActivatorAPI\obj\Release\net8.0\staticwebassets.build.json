{"Version": 1, "Hash": "jR7i4oQKckFp6wDsoPV97r9hQ5pZ/VmwuAVHdya8o6M=", "Source": "VehicleActivatorAPI", "BasePath": "_content/VehicleActivatorAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "VehicleActivatorAPI\\wwwroot", "Source": "VehicleActivatorAPI", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-BMw_JMhz#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b24jitss8o", "Integrity": "U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-BMw_JMhz.js", "FileLength": 180965, "LastWriteTime": "2025-06-25T06:48:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-H3RB1j-F#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b510bptf2a", "Integrity": "z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-H3RB1j-F.css", "FileLength": 1499, "LastWriteTime": "2025-06-25T06:48:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j2eqvkfzk9", "Integrity": "u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 464, "LastWriteTime": "2025-06-25T06:48:51+00:00"}], "Endpoints": [{"Route": "assets/index-BMw_JMhz.b24jitss8o.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b24jitss8o"}, {"Name": "label", "Value": "assets/index-BMw_JMhz.js"}, {"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-BMw_JMhz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-H3RB1j-F.b510bptf2a.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b510bptf2a"}, {"Name": "label", "Value": "assets/index-H3RB1j-F.css"}, {"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "assets/index-H3RB1j-F.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I="}]}, {"Route": "index.j2eqvkfzk9.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j2eqvkfzk9"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-u8iOG9TrAvckF4/z0lUimlkARS/4hoIarMD4FtC8f1I="}]}]}