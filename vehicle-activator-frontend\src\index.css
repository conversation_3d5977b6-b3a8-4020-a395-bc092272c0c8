* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1f2937;
  line-height: 1.5;
  min-height: 100vh;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-search {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  min-width: 100px;
  transition: background-color 0.2s;
}

.btn-search:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-search:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-clear {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 12px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.btn-clear:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-clear:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
  opacity: 0.6;
}

.search-info {
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
  margin-top: 5px;
}

.vehicles-table {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table tr:hover {
  background-color: #f8f9fa;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Status badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.activated {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.not-activated {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Activated row styling */
.activated-row {
  background-color: #f8f9fa !important;
  opacity: 0.8;
}

.activated-row:hover {
  background-color: #e9ecef !important;
}

.already-activated {
  color: #6c757d;
  font-style: italic;
  font-size: 14px;
}

/* Machine Log Activation Styles */
.activation-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.activation-section h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.activation-form {
  margin-bottom: 10px;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.machine-input {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.machine-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-danger:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Navigation Styles */
.nav-tabs {
  display: flex;
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.nav-tab {
  flex: 1;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background-color: #e9ecef;
  color: #007bff;
}

.nav-tab.active {
  background-color: #fff;
  color: #007bff;
  border-bottom-color: #007bff;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
    max-width: none;
    margin-bottom: 10px;
  }

  .btn-search,
  .btn-clear {
    width: 100%;
    margin-bottom: 5px;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .machine-input {
    min-width: auto;
    max-width: none;
    margin-bottom: 10px;
  }

  .nav-tabs {
    flex-direction: column;
  }

  .nav-tab {
    border-bottom: 1px solid #dee2e6;
    border-right: none;
  }

  .nav-tab.active {
    border-bottom: 1px solid #dee2e6;
    border-left: 3px solid #007bff;
  }

  .table {
    font-size: 14px;
  }

  .table th,
  .table td {
    padding: 8px;
  }
}
