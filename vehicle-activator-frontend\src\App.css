/* Mobile-first responsive design */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Container for mobile-first layout */
.app-container {
  width: 100%;
  min-height: 100vh;
  padding: 1rem;
  background-color: #f8fafc;
}

/* Responsive breakpoints */
@media (min-width: 768px) {
  .app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }
}

/* Header */
.app-header {
  text-align: center;
  margin-bottom: 2rem;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

@media (min-width: 768px) {
  .app-title {
    font-size: 2rem;
  }
}

/* Tab navigation - mobile optimized */
.tab-navigation {
  display: flex;
  width: 100%;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background-color: white;
  border-radius: 0.5rem 0.5rem 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  min-width: 140px;
  padding: 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-button.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
  background-color: #f8fafc;
}

.tab-button:hover {
  background-color: #f1f5f9;
  color: #1e40af;
}

/* Content area */
.tab-content {
  background-color: white;
  border-radius: 0 0 0.5rem 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

/* Mobile-first form styles */
.search-container {
  width: 100%;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

@media (min-width: 640px) {
  .search-form {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }
}

.search-input {
  flex: 1;
  padding: 0.875rem;
  border: 2px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background-color: #ffffff;
}

.search-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-button {
  padding: 0.875rem 1.5rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  min-width: 120px;
}

.search-button:hover {
  background-color: #1d4ed8;
}

.search-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Results container */
.results-container {
  padding: 1rem;
}

/* Mobile-first table styles */
.results-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Hide table on mobile, show cards instead */
@media (max-width: 767px) {
  .results-table {
    display: none;
  }
}

.results-table th,
.results-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.results-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.results-table tr:hover {
  background-color: #f9fafb;
}

/* Mobile card layout */
.mobile-cards {
  display: none;
}

@media (max-width: 767px) {
  .mobile-cards {
    display: block;
  }
}

.mobile-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.mobile-card-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
}

.mobile-card-content {
  display: grid;
  gap: 0.5rem;
}

.mobile-card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.mobile-card-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.mobile-card-value {
  color: #1f2937;
  font-size: 0.875rem;
  text-align: right;
}

/* Action buttons */
.action-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.activate-button {
  background-color: #10b981;
  color: white;
}

.activate-button:hover {
  background-color: #059669;
}

.activate-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.delete-button {
  background-color: #ef4444;
  color: white;
}

.delete-button:hover {
  background-color: #dc2626;
}

/* Status indicators */
.status-active {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: #d1fae5;
  color: #065f46;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-inactive {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: #fee2e2;
  color: #991b1b;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Loading and error states */
.loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #ef4444;
  background-color: #fef2f2;
  border-radius: 0.5rem;
  margin: 1rem;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Responsive utilities */
.hidden-mobile {
  display: block;
}

@media (max-width: 767px) {
  .hidden-mobile {
    display: none;
  }
}

.visible-mobile {
  display: none;
}

@media (max-width: 767px) {
  .visible-mobile {
    display: block;
  }
}
