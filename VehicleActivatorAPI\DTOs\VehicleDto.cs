namespace VehicleActivatorAPI.DTOs
{
    public class VehicleDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string? SoXe { get; set; }
        public string? SoChungtu { get; set; }
    }

    public class VehicleActivationRequest
    {
        public string Khoa { get; set; } = string.Empty;
        public string? NguoiDuyet { get; set; }
    }

    public class VehicleActivationResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? Id { get; set; }
    }
}
