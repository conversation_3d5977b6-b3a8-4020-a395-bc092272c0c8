import React, { useState, useEffect } from 'react';
import { machineLogApi } from '../services/api';

const MachineLogList = () => {
  const [machineLogs, setMachineLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [searchFilter, setSearchFilter] = useState('');
  const [activating, setActivating] = useState(false);
  const [newMachineId, setNewMachineId] = useState('');

  const fetchMachineLogs = async (filter = '') => {
    setLoading(true);
    setError('');
    try {
      const data = await machineLogApi.getMachineLogs(filter);
      setMachineLogs(data);
    } catch (err) {
      setError('Failed to fetch machine logs: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMachineLogs();
  }, []);

  const handleSearch = () => {
    if (searchInput.trim().length < 4) {
      setError('Please enter at least 4 characters to search');
      return;
    }
    setError('');
    setSearchFilter(searchInput.trim());
    fetchMachineLogs(searchInput.trim());
  };

  const handleClear = () => {
    setSearchInput('');
    setSearchFilter('');
    setError('');
    setSuccess('');
    fetchMachineLogs();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleActivate = async () => {
    if (!newMachineId.trim()) {
      setError('Please enter a Machine ID');
      return;
    }

    setActivating(true);
    setError('');
    setSuccess('');

    try {
      const result = await machineLogApi.activateMachineLog(newMachineId.trim());
      setSuccess(result.message);
      setNewMachineId('');
      // Refresh the list
      fetchMachineLogs(searchFilter);
    } catch (err) {
      setError('Failed to activate machine log: ' + err.message);
    } finally {
      setActivating(false);
    }
  };

  const handleDelete = async (id, machineId) => {
    if (!window.confirm(`Are you sure you want to delete machine log "${machineId}"?`)) {
      return;
    }

    try {
      await machineLogApi.deleteMachineLog(id);
      setSuccess(`Machine log "${machineId}" deleted successfully`);
      // Refresh the list
      fetchMachineLogs(searchFilter);
    } catch (err) {
      setError('Failed to delete machine log: ' + err.message);
    }
  };

  return (
    <div className="container">
      <div className="header">
        <h1>Machine Log Activator</h1>
        <p>Manage and activate machine logs in the HT_MachineLogs table</p>
      </div>

      {/* Activation Section */}
      <div className="activation-section">
        <h2>Activate New Machine Log</h2>
        <div className="activation-form">
          <div className="input-group">
            <input
              type="text"
              value={newMachineId}
              onChange={(e) => setNewMachineId(e.target.value)}
              placeholder="Enter Machine ID"
              className="machine-input"
              onKeyPress={(e) => e.key === 'Enter' && handleActivate()}
            />
            <button
              onClick={handleActivate}
              disabled={activating || !newMachineId.trim()}
              className="btn btn-primary"
            >
              {activating ? 'Activating...' : 'Activate Machine'}
            </button>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="search-container">
        <h2>Search Machine Logs</h2>
        <div className="search-input-group">
          <input
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Search by Machine ID (minimum 4 characters)"
            className="search-input"
          />
          <button
            onClick={handleSearch}
            disabled={loading || searchInput.trim().length < 4}
            className="btn-search"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
          <button
            onClick={handleClear}
            disabled={loading}
            className="btn-clear"
          >
            Clear
          </button>
        </div>
        {searchFilter && (
          <div className="search-info">
            Showing results for: "{searchFilter}" ({machineLogs.length} found)
          </div>
        )}
      </div>

      {/* Messages */}
      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      {/* Machine Logs Table */}
      <div className="vehicles-table">
        {loading ? (
          <div className="loading">Loading machine logs...</div>
        ) : machineLogs.length === 0 ? (
          <div className="no-data">
            {searchFilter ? 'No machine logs found matching your search.' : 'No machine logs found.'}
          </div>
        ) : (
          <table className="table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Machine ID</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {machineLogs.map((log) => (
                <tr key={log.id}>
                  <td>{log.id}</td>
                  <td>{log.machineId}</td>
                  <td>
                    <button
                      onClick={() => handleDelete(log.id, log.machineId)}
                      className="btn btn-danger"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default MachineLogList;
