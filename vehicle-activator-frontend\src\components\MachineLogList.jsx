import React, { useState, useEffect } from 'react';
import { machineLogApi } from '../services/api';

const MachineLogList = () => {
  const [machineLogs, setMachineLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [searchFilter, setSearchFilter] = useState('');
  const [activating, setActivating] = useState(false);
  const [newMachineId, setNewMachineId] = useState('');

  const fetchMachineLogs = async (filter = '') => {
    setLoading(true);
    setError('');
    try {
      const data = await machineLogApi.getMachineLogs(filter);
      setMachineLogs(data);
    } catch (err) {
      setError('Failed to fetch machine logs: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMachineLogs();
  }, []);

  const handleSearch = () => {
    if (searchInput.trim().length < 4) {
      setError('Please enter at least 4 characters to search');
      return;
    }
    setError('');
    setSearchFilter(searchInput.trim());
    fetchMachineLogs(searchInput.trim());
  };

  const handleClear = () => {
    setSearchInput('');
    setSearchFilter('');
    setError('');
    setSuccess('');
    fetchMachineLogs();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleActivate = async () => {
    if (!newMachineId.trim()) {
      setError('Please enter a Machine ID');
      return;
    }

    setActivating(true);
    setError('');
    setSuccess('');

    try {
      const result = await machineLogApi.activateMachineLog(newMachineId.trim());
      setSuccess(result.message);
      setNewMachineId('');
      // Refresh the list
      fetchMachineLogs(searchFilter);
    } catch (err) {
      setError('Failed to activate machine log: ' + err.message);
    } finally {
      setActivating(false);
    }
  };

  const handleDelete = async (id, machineId) => {
    if (!window.confirm(`Are you sure you want to delete machine log "${machineId}"?`)) {
      return;
    }

    try {
      await machineLogApi.deleteMachineLog(id);
      setSuccess(`Machine log "${machineId}" deleted successfully`);
      // Refresh the list
      fetchMachineLogs(searchFilter);
    } catch (err) {
      setError('Failed to delete machine log: ' + err.message);
    }
  };

  return (
    <div className="container">
      <div className="header">
        <h1>Machine Log Activator</h1>
        <p>Manage and activate machine logs in the HT_MachineLogs table</p>
      </div>

      {/* Activation Section */}
      <div className="search-container">
        <h3 style={{ margin: '0 0 1rem 0', color: '#1f2937', fontSize: '1.125rem' }}>🔧 Activate New Machine Log</h3>
        <form className="search-form" onSubmit={(e) => { e.preventDefault(); handleActivate(); }}>
          <input
            type="text"
            value={newMachineId}
            onChange={(e) => setNewMachineId(e.target.value)}
            placeholder="Enter Machine ID"
            className="search-input"
            onKeyPress={(e) => e.key === 'Enter' && handleActivate()}
          />
          <button
            type="submit"
            onClick={handleActivate}
            disabled={activating || !newMachineId.trim()}
            className="search-button"
            style={{ backgroundColor: '#10b981' }}
          >
            {activating ? '⚙️ Activating...' : '✅ Activate Machine'}
          </button>
        </form>
      </div>

      {/* Search Section */}
      <div className="search-container">
        <h3 style={{ margin: '0 0 1rem 0', color: '#1f2937', fontSize: '1.125rem' }}>🔍 Search Machine Logs</h3>
        <form className="search-form" onSubmit={(e) => { e.preventDefault(); handleSearch(); }}>
          <input
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Search by Machine ID (min 4 chars)"
            className="search-input"
          />
          <button
            type="submit"
            onClick={handleSearch}
            disabled={loading || searchInput.trim().length < 4}
            className="search-button"
          >
            {loading ? '🔍 Searching...' : '🔍 Search'}
          </button>
          <button
            type="button"
            onClick={handleClear}
            disabled={loading}
            className="search-button"
            style={{ backgroundColor: '#6b7280' }}
          >
            ✕ Clear
          </button>
        </form>
        {searchFilter && (
          <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
            Results for: "<strong>{searchFilter}</strong>" ({machineLogs.length} found)
          </div>
        )}
      </div>

      {/* Messages */}
      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      {/* Machine Logs Results */}
      <div className="results-container">
        {loading ? (
          <div className="loading">⚙️ Loading machine logs...</div>
        ) : machineLogs.length === 0 ? (
          <div className="no-results">
            {searchFilter ? 'No machine logs found matching your search.' : 'No machine logs found.'}
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <table className="results-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Machine ID</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {machineLogs.map((log) => (
                  <tr key={log.id}>
                    <td>{log.id}</td>
                    <td>{log.machineId}</td>
                    <td>
                      <button
                        onClick={() => handleDelete(log.id, log.machineId)}
                        className="action-button delete-button"
                      >
                        🗑️ Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Mobile Card View */}
            <div className="mobile-cards">
              {machineLogs.map((log) => (
                <div key={log.id} className="mobile-card">
                  <div className="mobile-card-header">
                    <div className="mobile-card-title">🔧 Machine #{log.id}</div>
                  </div>
                  <div className="mobile-card-content">
                    <div className="mobile-card-row">
                      <span className="mobile-card-label">Machine ID:</span>
                      <span className="mobile-card-value">{log.machineId}</span>
                    </div>
                    <div className="mobile-card-row">
                      <span className="mobile-card-label">Action:</span>
                      <div className="mobile-card-value">
                        <button
                          onClick={() => handleDelete(log.id, log.machineId)}
                          className="action-button delete-button"
                        >
                          🗑️ Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MachineLogList;
