{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "assets/index-BMw_JMhz.b24jitss8o.js", "AssetFile": "assets/index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b24jitss8o"}, {"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}, {"Name": "label", "Value": "assets/index-BMw_JMhz.js"}]}, {"Route": "assets/index-BMw_JMhz.js", "AssetFile": "assets/index-BMw_JMhz.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180965"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg="}]}, {"Route": "assets/index-CJChw8ry.3hojn27uz5.js", "AssetFile": "assets/index-CJChw8ry.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "182241"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3hojn27uz5"}, {"Name": "integrity", "Value": "sha256-WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs="}, {"Name": "label", "Value": "assets/index-CJChw8ry.js"}]}, {"Route": "assets/index-CJChw8ry.js", "AssetFile": "assets/index-CJChw8ry.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "182241"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs="}]}, {"Route": "assets/index-COJ0l26b.3fgy8k75fl.css", "AssetFile": "assets/index-COJ0l26b.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4666"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fgy8k75fl"}, {"Name": "integrity", "Value": "sha256-nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I="}, {"Name": "label", "Value": "assets/index-COJ0l26b.css"}]}, {"Route": "assets/index-COJ0l26b.css", "AssetFile": "assets/index-COJ0l26b.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4666"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I="}]}, {"Route": "assets/index-CpdsusLQ.css", "AssetFile": "assets/index-CpdsusLQ.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA="}]}, {"Route": "assets/index-CpdsusLQ.t1hsu86971.css", "AssetFile": "assets/index-CpdsusLQ.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:12:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1hsu86971"}, {"Name": "integrity", "Value": "sha256-rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA="}, {"Name": "label", "Value": "assets/index-CpdsusLQ.css"}]}, {"Route": "assets/index-H3RB1j-F.b510bptf2a.css", "AssetFile": "assets/index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b510bptf2a"}, {"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}, {"Name": "label", "Value": "assets/index-H3RB1j-F.css"}]}, {"Route": "assets/index-H3RB1j-F.css", "AssetFile": "assets/index-H3RB1j-F.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1499"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 06:48:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E="}]}, {"Route": "assets/index-QbAIgLC0.ikfqtwtpkd.js", "AssetFile": "assets/index-QbAIgLC0.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "186307"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ikfqtwtpkd"}, {"Name": "integrity", "Value": "sha256-Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM="}, {"Name": "label", "Value": "assets/index-QbAIgLC0.js"}]}, {"Route": "assets/index-QbAIgLC0.js", "AssetFile": "assets/index-QbAIgLC0.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "186307"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM="}]}, {"Route": "index.1hjrecezz3.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1hjrecezz3"}, {"Name": "integrity", "Value": "sha256-q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 25 Jun 2025 07:56:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q0aRCv2ViIO3zrmczySIV9eDekxUuGUXkd5mMfAP1vQ="}]}]}