# Vehicle Activator System - Deployment Guide

## 🚀 Single URL Deployment (Recommended)

Both the React frontend and .NET Core API are served from the same URL, making deployment and management much simpler.

### Quick Deployment

1. **Update Database Connection**
   ```json
   // In VehicleActivatorAPI/appsettings.json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=YOUR_SERVER;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```

2. **Build and Deploy**
   ```bash
   # Run the integrated build script
   publish-production.bat
   ```

3. **Run in Production**
   ```bash
   # Navigate to the publish directory
   cd publish\integrated
   
   # Update connection string if needed
   # Edit appsettings.Production.json
   
   # Run the application
   run-production.bat
   ```

4. **Access the Application**
   - **Web Interface**: https://localhost:5001/
   - **API Endpoints**: https://localhost:5001/api/
   - **Swagger UI**: https://localhost:5001/swagger (Development only)

## 🛠️ Development Modes

### Option 1: Integrated Development
```bash
# Builds React and serves from .NET API
run-integrated.bat
```
- Access at: https://localhost:7000/
- Both frontend and API from same URL
- Production-like experience

### Option 2: Separate Development Servers
```bash
# Runs React dev server + .NET API separately
build-and-run.bat
```
- React Dev Server: http://localhost:3000/ (with hot reload)
- .NET API: https://localhost:7000/api/
- Integrated View: https://localhost:7000/ (proxies to React)

## 📁 Deployment Structure

After running `publish-production.bat`:

```
publish/
└── integrated/
    ├── VehicleActivatorAPI.exe          # Main application
    ├── appsettings.json                 # Default settings
    ├── appsettings.Production.json      # Production settings
    ├── run-production.bat               # Startup script
    ├── wwwroot/                         # React build files
    │   ├── index.html
    │   ├── assets/
    │   └── ...
    └── ... (other .NET files)
```

## 🔧 Configuration

### Production Settings
Update `publish/integrated/appsettings.Production.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_PRODUCTION_SERVER;Database=CARSOFT_GIAPHAT;Integrated Security=true;TrustServerCertificate=true;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Urls": "https://localhost:5001;http://localhost:5000"
}
```

### Custom Port Configuration
To run on different ports, update the `Urls` setting:
```json
{
  "Urls": "https://localhost:8443;http://localhost:8080"
}
```

## 🌐 IIS Deployment

1. **Publish the Application**
   ```bash
   publish-production.bat
   ```

2. **Copy to IIS Directory**
   ```bash
   xcopy "publish\integrated\*" "C:\inetpub\wwwroot\VehicleActivator\" /E /Y
   ```

3. **Configure IIS**
   - Create new Application Pool (.NET Core)
   - Create new Website pointing to the directory
   - Ensure proper permissions for database access

4. **Update Connection String**
   - Edit `appsettings.Production.json` with production database settings

## 🐳 Docker Deployment

Create `Dockerfile` in the root directory:
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy and restore .NET project
COPY VehicleActivatorAPI/*.csproj VehicleActivatorAPI/
RUN dotnet restore VehicleActivatorAPI/VehicleActivatorAPI.csproj

# Copy and build React app
COPY vehicle-activator-frontend/package*.json vehicle-activator-frontend/
RUN cd vehicle-activator-frontend && npm install

COPY vehicle-activator-frontend/ vehicle-activator-frontend/
RUN cd vehicle-activator-frontend && npm run build

# Copy React build to .NET wwwroot
RUN mkdir -p VehicleActivatorAPI/wwwroot
RUN cp -r vehicle-activator-frontend/dist/* VehicleActivatorAPI/wwwroot/

# Copy and build .NET app
COPY VehicleActivatorAPI/ VehicleActivatorAPI/
RUN dotnet build VehicleActivatorAPI/VehicleActivatorAPI.csproj -c Release -o /app/build

FROM build AS publish
RUN dotnet publish VehicleActivatorAPI/VehicleActivatorAPI.csproj -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "VehicleActivatorAPI.dll"]
```

## 🧪 Testing Deployment

### Test Scripts Available:
- `test-integrated.bat` - Test the integrated deployment
- `test-api.bat` - Test API endpoints only
- `test-frontend.html` - Test frontend in browser

### Manual Testing:
1. **Frontend**: Navigate to https://localhost:5001/
2. **API**: Test https://localhost:5001/api/vehicles
3. **Swagger**: Visit https://localhost:5001/swagger

## 🔒 Security Considerations

1. **HTTPS Configuration**
   - Configure proper SSL certificates for production
   - Update `appsettings.Production.json` with production URLs

2. **Database Security**
   - Use proper SQL Server authentication
   - Implement connection string encryption
   - Configure firewall rules

3. **Application Security**
   - Implement authentication/authorization
   - Add input validation
   - Configure CORS for production domains

## 📊 Monitoring

- Check application logs in the deployment directory
- Monitor database connections
- Set up health checks for production monitoring

## 🆘 Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Verify connection string in `appsettings.Production.json`
   - Check SQL Server accessibility
   - Verify database exists

2. **Frontend Not Loading**
   - Ensure React build files are in `wwwroot` directory
   - Check for JavaScript errors in browser console

3. **API Endpoints Not Working**
   - Verify .NET application is running
   - Check firewall settings
   - Review application logs

### Log Locations:
- Application logs: Console output or configured log files
- IIS logs: `C:\inetpub\logs\LogFiles\`
- Windows Event Logs: Application and System logs
