# Vehicle Activator System

A web application built with .NET Core 8 Web API and React for managing vehicle activation from the CARSOFT_GIAPHAT database.

## Features

- **Vehicle List Display**: Shows vehicles from SC_BaoGia table with SoXe and SoChungTu columns
- **Search Functionality**: Filter vehicles by SoXe (Vehicle Number)
- **Vehicle Activation**: Activate vehicles by inserting records into Temp_HoSoXeHoanTat table
- **Real-time Updates**: Activated vehicles are removed from the list immediately
- **Error Handling**: Comprehensive error handling and user feedback

## Architecture

### Backend (.NET Core 8 Web API)
- **Models**: Entity Framework models for SC_BaoGia and Temp_HoSoXeHoanTat
- **Controllers**: RESTful API endpoints for vehicle operations
- **Database**: SQL Server with Entity Framework Core
- **CORS**: Configured for React frontend communication

### Frontend (React with Vite)
- **Components**: Modular React components for vehicle management
- **API Integration**: Axios for HTTP requests to backend
- **Responsive Design**: Clean, modern UI with CSS styling
- **Real-time Search**: Debounced search functionality

## Database Schema

### SC_BaoGia Table
```sql
- Khoa (Primary Key)
- SoChungtu
- NgayChungTu
- KhoaXe
- KhoaLoaiXe
- SoXe
- SoKmHienTai
- SoKmTruoc
```

### Temp_HoSoXeHoanTat Table
```sql
- Id (Identity, Primary Key)
- KhoaBaoGia (nchar(50))
- NguoiDuyet (nvarchar(150))
- NgayDuyet (datetime)
```

## Setup Instructions

### Prerequisites
- .NET 8 SDK
- Node.js (v18 or higher)
- SQL Server
- CARSOFT_GIAPHAT database

### Backend Setup
1. Navigate to VehicleActivatorAPI directory
2. Update connection string in `appsettings.json`
3. Run the API:
   ```bash
   dotnet restore
   dotnet run
   ```

### Frontend Setup
1. Navigate to vehicle-activator-frontend directory
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start development server:
   ```bash
   npm run dev
   ```

### Database Configuration
Update the connection string in `VehicleActivatorAPI/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

## API Endpoints

- `GET /api/vehicles` - Get vehicles list (with optional soXeFilter query parameter)
- `POST /api/vehicles/activate` - Activate a vehicle
- `GET /api/vehicles/activated` - Get list of activated vehicles

## Usage

1. **View Vehicles**: The application loads and displays vehicles from the database
2. **Search**: Use the search box to filter vehicles by SoXe
3. **Activate**: Click the "Activate" button next to any vehicle to activate it
4. **Feedback**: Success/error messages are displayed for user actions

## Production Build

### Backend
```bash
cd VehicleActivatorAPI
dotnet publish -c Release -o ./publish
```

### Frontend
```bash
cd vehicle-activator-frontend
npm run build
```

The built files will be in the `dist` directory and can be served by any static file server or integrated with the .NET Core application.

## Security Considerations

- Update connection strings for production
- Implement proper authentication and authorization
- Add input validation and sanitization
- Configure HTTPS certificates for production
- Implement rate limiting and request validation
