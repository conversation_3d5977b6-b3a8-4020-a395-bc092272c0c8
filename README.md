# Vehicle Activator System

A web application built with .NET Core 8 Web API and React for managing vehicle activation from the CARSOFT_GIAPHAT database.

## Features

- **Vehicle List Display**: Shows vehicles from SC_BaoGia table with SoXe and SoChungTu columns
- **Search Functionality**: Filter vehicles by SoXe (Vehicle Number)
- **Vehicle Activation**: Activate vehicles by inserting records into Temp_HoSoXeHoanTat table
- **Real-time Updates**: Activated vehicles are removed from the list immediately
- **Error Handling**: Comprehensive error handling and user feedback

## Architecture

### Backend (.NET Core 8 Web API)
- **Models**: Entity Framework models for SC_BaoGia and Temp_HoSoXeHoanTat
- **Controllers**: RESTful API endpoints for vehicle operations
- **Database**: SQL Server with Entity Framework Core
- **CORS**: Configured for React frontend communication

### Frontend (React with Vite)
- **Components**: Modular React components for vehicle management
- **API Integration**: Axios for HTTP requests to backend
- **Responsive Design**: Clean, modern UI with CSS styling
- **Real-time Search**: Debounced search functionality

## Database Schema

### SC_BaoGia Table
```sql
- Khoa (Primary Key)
- SoChungtu
- NgayChungTu
- KhoaXe
- KhoaLoaiXe
- SoXe
- SoKmHienTai
- SoKmTruoc
```

### Temp_HoSoXeHoanTat Table
```sql
- Id (Identity, Primary Key)
- KhoaBaoGia (nchar(50))
- NguoiDuyet (nvarchar(150))
- NgayDuyet (datetime)
```

## Setup Instructions

### Prerequisites
- .NET 8 SDK
- Node.js (v18 or higher)
- SQL Server
- CARSOFT_GIAPHAT database

### Quick Start (Integrated - Single URL)
1. Update connection string in `VehicleActivatorAPI/appsettings.json`
2. Run `run-integrated.bat` - This will:
   - Build React frontend for production
   - Copy build files to .NET API wwwroot
   - Start integrated application on single URL
   - Access at: https://localhost:7000/

### Development Mode (Separate URLs)
1. Run `build-and-run.bat` - This will:
   - Start React dev server: http://localhost:3000
   - Start .NET API: https://localhost:7000
   - Integrated view: https://localhost:7000/ (recommended)

### Database Configuration
Update the connection string in `VehicleActivatorAPI/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

## API Endpoints

- `GET /api/vehicles` - Get vehicles list (with optional soXeFilter query parameter)
- `POST /api/vehicles/activate` - Activate a vehicle
- `GET /api/vehicles/activated` - Get list of activated vehicles

## Usage

1. **View Vehicles**: The application loads and displays vehicles from the database
2. **Search**: Use the search box to filter vehicles by SoXe
3. **Activate**: Click the "Activate" button next to any vehicle to activate it
4. **Feedback**: Success/error messages are displayed for user actions

## Production Build

### Integrated Deployment (Single URL)
```bash
# Run the integrated build script
publish-production.bat
```

This creates a single deployment package where:
- Frontend and API are served from the same URL
- Files are located in `publish/integrated/`
- Run with `publish/integrated/run-production.bat`
- Access everything at: https://localhost:5001/

### Manual Build Steps
```bash
# Build React frontend
cd vehicle-activator-frontend
npm run build

# Copy to API wwwroot
cd ../VehicleActivatorAPI
xcopy "../vehicle-activator-frontend/dist/*" "wwwroot/" /E /Y

# Publish integrated application
dotnet publish -c Release -o ../publish/integrated
```

## Security Considerations

- Update connection strings for production
- Implement proper authentication and authorization
- Add input validation and sanitization
- Configure HTTPS certificates for production
- Implement rate limiting and request validation
