<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Activator API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vehicle Activator API Test</h1>
        <p>This page tests the API endpoints directly from the browser.</p>
        
        <div>
            <button onclick="testGetVehicles()">Test GET Vehicles</button>
            <button onclick="testGetVehiclesWithFilter()">Test GET Vehicles (Filtered)</button>
            <button onclick="testGetActivatedVehicles()">Test GET Activated Vehicles</button>
            <button onclick="testActivateVehicle()">Test Activate Vehicle</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // Use relative URL for integrated deployment, absolute for testing
        const API_BASE = window.location.origin + '/api';
        
        function addResult(title, data, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${JSON.stringify(data, null, 2)}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testGetVehicles() {
            try {
                const response = await fetch(`${API_BASE}/vehicles`);
                const data = await response.json();
                addResult('GET /api/vehicles', data);
            } catch (error) {
                addResult('GET /api/vehicles - ERROR', error.message, true);
            }
        }
        
        async function testGetVehiclesWithFilter() {
            try {
                const response = await fetch(`${API_BASE}/vehicles?soXeFilter=test`);
                const data = await response.json();
                addResult('GET /api/vehicles?soXeFilter=test', data);
            } catch (error) {
                addResult('GET /api/vehicles (filtered) - ERROR', error.message, true);
            }
        }
        
        async function testGetActivatedVehicles() {
            try {
                const response = await fetch(`${API_BASE}/vehicles/activated`);
                const data = await response.json();
                addResult('GET /api/vehicles/activated', data);
            } catch (error) {
                addResult('GET /api/vehicles/activated - ERROR', error.message, true);
            }
        }
        
        async function testActivateVehicle() {
            try {
                const testData = {
                    khoa: 'TEST_KHOA_' + Date.now(),
                    nguoiDuyet: 'Test User'
                };
                
                const response = await fetch(`${API_BASE}/vehicles/activate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                addResult('POST /api/vehicles/activate', data);
            } catch (error) {
                addResult('POST /api/vehicles/activate - ERROR', error.message, true);
            }
        }
        
        // Clear results
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Add clear button
        document.addEventListener('DOMContentLoaded', function() {
            const buttonContainer = document.querySelector('div');
            const clearButton = document.createElement('button');
            clearButton.textContent = 'Clear Results';
            clearButton.onclick = clearResults;
            clearButton.style.background = '#6c757d';
            buttonContainer.appendChild(clearButton);
        });
    </script>
</body>
</html>
