using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using VehicleActivatorAPI.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add Entity Framework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add CORS for development
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        policy =>
        {
            policy.WithOrigins("http://localhost:3000", "http://localhost:5173")
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });
});

// Add SPA services
builder.Services.AddSpaStaticFiles(configuration =>
{
    configuration.RootPath = "wwwroot";
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Use CORS only in development
if (app.Environment.IsDevelopment())
{
    app.UseCors("AllowReactApp");
}

// Serve static files from wwwroot
app.UseStaticFiles();

// Serve SPA static files
app.UseSpaStaticFiles();

app.UseAuthorization();

app.MapControllers();

// Configure SPA
app.UseSpa(spa =>
{
    spa.Options.SourcePath = "../vehicle-activator-frontend";

    if (app.Environment.IsDevelopment())
    {
        // In development, proxy to React dev server
        spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
    }
    else
    {
        // In production, serve from wwwroot
        spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions
        {
            FileProvider = new PhysicalFileProvider(Path.Combine(app.Environment.ContentRootPath, "wwwroot"))
        };
    }
});

app.Run();
