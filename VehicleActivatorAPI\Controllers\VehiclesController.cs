using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using VehicleActivatorAPI.Data;
using VehicleActivatorAPI.DTOs;
using VehicleActivatorAPI.Models;

namespace VehicleActivatorAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class VehiclesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<VehiclesController> _logger;

        public VehiclesController(ApplicationDbContext context, ILogger<VehiclesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<VehicleDto>>> GetVehicles([FromQuery] string? soXeFilter = null)
        {
            try
            {
                var query = _context.ScBaoGia.AsQueryable();

                if (!string.IsNullOrEmpty(soXeFilter))
                {
                    query = query.Where(v => v.SoXe != null && v.SoXe.Contains(soXeFilter));
                }

                // First get vehicles
                var vehicleList = await query.Take(1000).ToListAsync();

                // Then get activation status separately to avoid casting issues
                var vehicleKeys = vehicleList.Select(v => v.Khoa).ToList();
                var activations = await _context.TempHoSoXeHoanTat
                    .Where(t => vehicleKeys.Contains(t.KhoaBaoGia))
                    .ToListAsync();

                var vehicles = vehicleList.Select(v =>
                {
                    var activation = activations.FirstOrDefault(a => a.KhoaBaoGia == v.Khoa);
                    return new VehicleDto
                    {
                        Khoa = v.Khoa,
                        SoXe = v.SoXe,
                        SoChungtu = v.SoChungtu,
                        IsActivated = activation != null,
                        NgayDuyet = activation?.NgayDuyet,
                        NguoiDuyet = activation?.NguoiDuyet
                    };
                }).ToList();

                return Ok(vehicles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vehicles");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("activate")]
        public async Task<ActionResult<VehicleActivationResponse>> ActivateVehicle([FromBody] VehicleActivationRequest request)
        {
            try
            {
                // Check if vehicle exists
                var vehicle = await _context.ScBaoGia.FindAsync(request.Khoa);
                if (vehicle == null)
                {
                    return NotFound(new VehicleActivationResponse
                    {
                        Success = false,
                        Message = "Vehicle not found"
                    });
                }

                // Check if already activated
                var existingActivation = await _context.TempHoSoXeHoanTat
                    .FirstOrDefaultAsync(t => t.KhoaBaoGia == request.Khoa);

                if (existingActivation != null)
                {
                    return BadRequest(new VehicleActivationResponse
                    {
                        Success = false,
                        Message = "Vehicle is already activated"
                    });
                }

                // Create new activation record
                var activation = new TempHoSoXeHoanTat
                {
                    KhoaBaoGia = request.Khoa,
                    NguoiDuyet = request.NguoiDuyet ?? "System",
                    NgayDuyet = DateTime.Now
                };

                _context.TempHoSoXeHoanTat.Add(activation);
                await _context.SaveChangesAsync();

                return Ok(new VehicleActivationResponse
                {
                    Success = true,
                    Message = "Vehicle activated successfully",
                    Id = activation.Id
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating vehicle with Khoa: {Khoa}", request.Khoa);
                return StatusCode(500, new VehicleActivationResponse
                {
                    Success = false,
                    Message = "Internal server error"
                });
            }
        }

        [HttpGet("activated")]
        public async Task<ActionResult<IEnumerable<TempHoSoXeHoanTat>>> GetActivatedVehicles()
        {
            try
            {
                var activatedVehicles = await _context.TempHoSoXeHoanTat
                    .OrderByDescending(t => t.NgayDuyet)
                    .ToListAsync();

                return Ok(activatedVehicles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving activated vehicles");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
