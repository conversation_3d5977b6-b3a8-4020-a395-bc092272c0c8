import axios from 'axios';

// Use relative URL in production, absolute URL in development
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'
  : 'https://localhost:7000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const vehicleApi = {
  // Get vehicles with optional filter
  getVehicles: async (soXeFilter = '') => {
    try {
      const params = soXeFilter ? { soXeFilter } : {};
      const response = await api.get('/vehicles', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      throw error;
    }
  },

  // Activate a vehicle
  activateVehicle: async (khoa, nguoiDuyet = 'System') => {
    try {
      const response = await api.post('/vehicles/activate', {
        khoa,
        nguoiDuyet
      });
      return response.data;
    } catch (error) {
      console.error('Error activating vehicle:', error);
      throw error;
    }
  },

  // Get activated vehicles
  getActivatedVehicles: async () => {
    try {
      const response = await api.get('/vehicles/activated');
      return response.data;
    } catch (error) {
      console.error('Error fetching activated vehicles:', error);
      throw error;
    }
  }
};

export default api;
