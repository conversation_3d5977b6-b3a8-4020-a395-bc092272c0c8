using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VehicleActivatorAPI.Models
{
    [Table("SC_BaoGia")]
    public class ScBaoGia
    {
        [Key]
        public string Khoa { get; set; } = string.Empty;
        
        public string? SoChungtu { get; set; }
        
        public DateTime? NgayChungTu { get; set; }
        
        public string? KhoaXe { get; set; }
        
        public string? KhoaLoaiXe { get; set; }
        
        public string? SoXe { get; set; }
        
        public int? SoKmHienTai { get; set; }
        
        public int? SoKmTruoc { get; set; }
    }
}
