using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VehicleActivatorAPI.Models
{
    [Table("SC_BaoGia")]
    public class ScBaoGia
    {
        [Key]
        public string Khoa { get; set; } = string.Empty;
        
        public string? SoChungtu { get; set; }
        
        [Column(TypeName = "nchar(8)")]
        public string? NgayChungTuString { get; set; }

        [NotMapped]
        public DateTime? NgayChungTu
        {
            get
            {
                if (string.IsNullOrWhiteSpace(NgayChungTuString))
                    return null;

                // Parse date in format YYYYMMDD (8 characters)
                if (NgayChungTuString.Length == 8 &&
                    DateTime.TryParseExact(NgayChungTuString.Trim(), "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime result))
                    return result;

                return null;
            }
            set
            {
                NgayChungTuString = value?.ToString("yyyyMMdd");
            }
        }
        
        public string? KhoaXe { get; set; }
        
        public string? KhoaLoaiXe { get; set; }
        
        public string? SoXe { get; set; }
        
        [Column(TypeName = "numeric(18,0)")]
        public decimal? SoKmHienTaiDecimal { get; set; }

        [NotMapped]
        public int? SoKmHienTai
        {
            get => SoKmHienTaiDecimal.HasValue ? (int?)Convert.ToInt32(SoKmHienTaiDecimal.Value) : null;
            set => SoKmHienTaiDecimal = value.HasValue ? (decimal?)value.Value : null;
        }

        [Column(TypeName = "numeric(18,0)")]
        public decimal? SoKmTruocDecimal { get; set; }

        [NotMapped]
        public int? SoKmTruoc
        {
            get => SoKmTruocDecimal.HasValue ? (int?)Convert.ToInt32(SoKmTruocDecimal.Value) : null;
            set => SoKmTruocDecimal = value.HasValue ? (decimal?)value.Value : null;
        }
    }
}
