using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using VehicleActivatorAPI.Data;
using VehicleActivatorAPI.DTOs;
using VehicleActivatorAPI.Models;

namespace VehicleActivatorAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MachineLogsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MachineLogsController> _logger;

        public MachineLogsController(ApplicationDbContext context, ILogger<MachineLogsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<MachineLogDto>>> GetMachineLogs([FromQuery] string? filter = null)
        {
            try
            {
                var query = _context.HtMachineLogs.AsQueryable();

                if (!string.IsNullOrWhiteSpace(filter))
                {
                    query = query.Where(m => m.MachineId.Contains(filter));
                }

                var machineLogs = await query
                    .Select(m => new MachineLogDto
                    {
                        Id = m.Id,
                        MachineId = m.MachineId
                    })
                    .OrderBy(m => m.MachineId)
                    .Take(1000) // Limit results
                    .ToListAsync();

                return Ok(machineLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching machine logs");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("activate")]
        public async Task<ActionResult<MachineLogActivationResponse>> ActivateMachineLog([FromBody] MachineLogActivationRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.MachineId))
                {
                    return BadRequest(new MachineLogActivationResponse
                    {
                        Success = false,
                        Message = "MachineId is required"
                    });
                }

                // Check if machine log already exists
                var existingLog = await _context.HtMachineLogs
                    .FirstOrDefaultAsync(m => m.MachineId == request.MachineId);

                if (existingLog != null)
                {
                    return BadRequest(new MachineLogActivationResponse
                    {
                        Success = false,
                        Message = $"Machine log with ID '{request.MachineId}' already exists"
                    });
                }

                // Create new machine log
                var machineLog = new HtMachineLog
                {
                    MachineId = request.MachineId.Trim()
                };

                _context.HtMachineLogs.Add(machineLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Machine log activated successfully for MachineId: {MachineId}", request.MachineId);

                return Ok(new MachineLogActivationResponse
                {
                    Success = true,
                    Message = $"Machine log activated successfully for ID: {request.MachineId}",
                    Id = machineLog.Id
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating machine log with MachineId: {MachineId}", request.MachineId);
                return StatusCode(500, new MachineLogActivationResponse
                {
                    Success = false,
                    Message = "Internal server error occurred while activating machine log"
                });
            }
        }

        [HttpGet("activated")]
        public async Task<ActionResult<IEnumerable<MachineLogDto>>> GetActivatedMachineLogs()
        {
            try
            {
                var activatedLogs = await _context.HtMachineLogs
                    .Select(m => new MachineLogDto
                    {
                        Id = m.Id,
                        MachineId = m.MachineId
                    })
                    .OrderByDescending(m => m.Id)
                    .ToListAsync();

                return Ok(activatedLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching activated machine logs");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteMachineLog(int id)
        {
            try
            {
                var machineLog = await _context.HtMachineLogs.FindAsync(id);
                if (machineLog == null)
                {
                    return NotFound($"Machine log with ID {id} not found");
                }

                _context.HtMachineLogs.Remove(machineLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Machine log deleted successfully: {Id}", id);
                return Ok($"Machine log with ID {id} deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting machine log with ID: {Id}", id);
                return StatusCode(500, "Internal server error occurred while deleting machine log");
            }
        }
    }
}
