import React, { useState, useEffect } from 'react';
import { vehicleApi } from '../services/api';

const VehicleList = () => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchFilter, setSearchFilter] = useState('');
  const [activatingVehicles, setActivatingVehicles] = useState(new Set());

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async (filter = '') => {
    try {
      setLoading(true);
      setError('');
      const data = await vehicleApi.getVehicles(filter);
      setVehicles(data);
    } catch (err) {
      setError('Failed to fetch vehicles. Please check your connection and try again.');
      console.error('Fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchFilter(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      fetchVehicles(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleActivateVehicle = async (vehicle) => {
    try {
      setActivatingVehicles(prev => new Set([...prev, vehicle.khoa]));
      setError('');
      setSuccess('');

      const result = await vehicleApi.activateVehicle(vehicle.khoa, 'Current User');
      
      if (result.success) {
        setSuccess(`Vehicle ${vehicle.soXe} activated successfully!`);
        // Remove the activated vehicle from the list
        setVehicles(prev => prev.filter(v => v.khoa !== vehicle.khoa));
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || 'Failed to activate vehicle');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to activate vehicle. Please try again.';
      setError(errorMessage);
      console.error('Activation error:', err);
    } finally {
      setActivatingVehicles(prev => {
        const newSet = new Set(prev);
        newSet.delete(vehicle.khoa);
        return newSet;
      });
    }
  };

  if (loading) {
    return <div className="loading">Loading vehicles...</div>;
  }

  return (
    <div>
      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}
      
      <div className="search-container">
        <input
          type="text"
          placeholder="Search by Vehicle Number (SoXe)..."
          value={searchFilter}
          onChange={handleSearch}
          className="search-input"
        />
      </div>

      <div className="vehicles-table">
        {vehicles.length === 0 ? (
          <div className="no-data">No vehicles found</div>
        ) : (
          <table className="table">
            <thead>
              <tr>
                <th>Vehicle Number (SoXe)</th>
                <th>Document Number (SoChungTu)</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {vehicles.map((vehicle) => (
                <tr key={vehicle.khoa}>
                  <td>{vehicle.soXe || 'N/A'}</td>
                  <td>{vehicle.soChungtu || 'N/A'}</td>
                  <td>
                    <button
                      className="btn btn-primary"
                      onClick={() => handleActivateVehicle(vehicle)}
                      disabled={activatingVehicles.has(vehicle.khoa)}
                    >
                      {activatingVehicles.has(vehicle.khoa) ? 'Activating...' : 'Activate'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default VehicleList;
