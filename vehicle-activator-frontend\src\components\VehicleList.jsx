import React, { useState, useEffect } from 'react';
import { vehicleApi } from '../services/api';

const VehicleList = () => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [searchFilter, setSearchFilter] = useState('');
  const [activatingVehicles, setActivatingVehicles] = useState(new Set());
  const [hasSearched, setHasSearched] = useState(false);

  const fetchVehicles = async (filter = '') => {
    try {
      setSearching(true);
      setError('');
      const data = await vehicleApi.getVehicles(filter);
      setVehicles(data);
      setHasSearched(true);
    } catch (err) {
      setError('Failed to fetch vehicles. Please check your connection and try again.');
      console.error('Fetch error:', err);
    } finally {
      setSearching(false);
    }
  };

  const handleSearch = () => {
    if (searchInput.trim().length < 4) {
      setError('Please enter at least 4 characters to search');
      return;
    }
    setError('');
    setSearchFilter(searchInput.trim());
    fetchVehicles(searchInput.trim());
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setSearchFilter('');
    setVehicles([]);
    setHasSearched(false);
    setError('');
    setSuccess('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleInputChange = (e) => {
    setSearchInput(e.target.value);
    if (error && error.includes('at least 4 characters')) {
      setError('');
    }
  };

  const handleActivateVehicle = async (vehicle) => {
    try {
      setActivatingVehicles(prev => new Set([...prev, vehicle.khoa]));
      setError('');
      setSuccess('');

      const result = await vehicleApi.activateVehicle(vehicle.khoa, 'Current User');
      
      if (result.success) {
        setSuccess(`Vehicle ${vehicle.soXe} activated successfully!`);
        // Remove the activated vehicle from the list
        setVehicles(prev => prev.filter(v => v.khoa !== vehicle.khoa));
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || 'Failed to activate vehicle');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to activate vehicle. Please try again.';
      setError(errorMessage);
      console.error('Activation error:', err);
    } finally {
      setActivatingVehicles(prev => {
        const newSet = new Set(prev);
        newSet.delete(vehicle.khoa);
        return newSet;
      });
    }
  };

  const getStatusBadge = (vehicle) => {
    if (vehicle.isActivated) {
      return (
        <span className="status-active" title={`Activated by ${vehicle.nguoiDuyet || 'Unknown'} on ${vehicle.ngayDuyet ? new Date(vehicle.ngayDuyet).toLocaleDateString() : 'Unknown date'}`}>
          ✓ Activated
        </span>
      );
    }
    return <span className="status-inactive">⚪ Not Activated</span>;
  };

  return (
    <div>
      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      <div className="search-container">
        <form className="search-form" onSubmit={(e) => { e.preventDefault(); handleSearch(); }}>
          <input
            type="text"
            placeholder="Search by Vehicle Number (min 4 chars)..."
            value={searchInput}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="search-input"
          />
          <button
            type="submit"
            className="search-button"
            disabled={searching || searchInput.trim().length < 4}
          >
            {searching ? '🔍 Searching...' : '🔍 Search'}
          </button>
          {(hasSearched || searchFilter) && (
            <button
              type="button"
              className="search-button"
              onClick={handleClearSearch}
              disabled={searching}
              style={{ backgroundColor: '#6b7280' }}
            >
              ✕ Clear
            </button>
          )}
        </form>
        {searchFilter && (
          <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
            Results for: "<strong>{searchFilter}</strong>" ({vehicles.length} found)
          </div>
        )}
      </div>

      <div className="results-container">
        {searching ? (
          <div className="loading">🔍 Searching vehicles...</div>
        ) : !hasSearched ? (
          <div className="no-results">Enter at least 4 characters and click Search to find vehicles</div>
        ) : vehicles.length === 0 ? (
          <div className="no-results">No vehicles found for "{searchFilter}"</div>
        ) : (
          <>
            {/* Desktop Table View */}
            <table className="results-table">
              <thead>
                <tr>
                  <th>Vehicle Number</th>
                  <th>Document Number</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {vehicles.map((vehicle) => (
                  <tr key={vehicle.khoa} className={vehicle.isActivated ? 'activated-row' : ''}>
                    <td>{vehicle.soXe || 'N/A'}</td>
                    <td>{vehicle.soChungtu || 'N/A'}</td>
                    <td>{getStatusBadge(vehicle)}</td>
                    <td>
                      {vehicle.isActivated ? (
                        <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Already Activated</span>
                      ) : (
                        <button
                          className="action-button activate-button"
                          onClick={() => handleActivateVehicle(vehicle)}
                          disabled={activatingVehicles.has(vehicle.khoa)}
                        >
                          {activatingVehicles.has(vehicle.khoa) ? 'Activating...' : 'Activate'}
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Mobile Card View */}
            <div className="mobile-cards">
              {vehicles.map((vehicle) => (
                <div key={vehicle.khoa} className="mobile-card">
                  <div className="mobile-card-header">
                    <div className="mobile-card-title">🚗 {vehicle.soXe || 'N/A'}</div>
                    {getStatusBadge(vehicle)}
                  </div>
                  <div className="mobile-card-content">
                    <div className="mobile-card-row">
                      <span className="mobile-card-label">Document Number:</span>
                      <span className="mobile-card-value">{vehicle.soChungtu || 'N/A'}</span>
                    </div>
                    <div className="mobile-card-row">
                      <span className="mobile-card-label">Action:</span>
                      <div className="mobile-card-value">
                        {vehicle.isActivated ? (
                          <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Already Activated</span>
                        ) : (
                          <button
                            className="action-button activate-button"
                            onClick={() => handleActivateVehicle(vehicle)}
                            disabled={activatingVehicles.has(vehicle.khoa)}
                          >
                            {activatingVehicles.has(vehicle.khoa) ? 'Activating...' : 'Activate'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default VehicleList;
