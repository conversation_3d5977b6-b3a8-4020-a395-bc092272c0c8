{"GlobalPropertiesHash": "JXSxunYq4mSTA+3MPVsBkmq2RWyLlHLxbffaZT5XrIw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["AYzx14NcHTcyKMKmp6N6FHnzqkdUu+xcQFgYIz3WGvA=", "9+BOV7BTa5hvpT3mGFmC4L5PR6JaYnCXp3WWQzmIwso=", "Yotrz+1uFXTHyh5b1+2boAWTMq6GxVG32onQxX1v9pI=", "bXFR1r6gfMrA7YoF78l4mMn+plUAyWGYEKgVOnKebOA=", "L9Y+YmKvnIPT3Oo6C3C9lA9apruradpA3qis5iZX7NM=", "tZNlYuXVGTSH/JLJHogZoh4Fqb7YnUZVz9I7dnQoolc=", "W82oTJhmNgmqRqDKgG25sfb/VwYj/4MQ5uZRKpnVc/k=", "h/bDKwo7BkPfDY48VLsuCG2+HO2L5Vssssx/tKforMY=", "sckqxnt+QAtLuuFJPkF4aD08mGiINiajx0RkOitaHuc=", "SHLylOPIVhiBC5Z4QchdmXwkTyZxclXAwyurX5ZZH9g=", "VEfCJpxRQhJ3K3Cy6a9GLHrF/X9R3zV9pS8OXKhmQPY=", "U43mX+tpXU+OfNwWP6SLFt+qvtr04qhKpE1UhcrIf2s=", "f2IoASa/kSOxN0DTNgVwVU2YM3nO0O8Wkcy3b79wSTM="], "CachedAssets": {"AYzx14NcHTcyKMKmp6N6FHnzqkdUu+xcQFgYIz3WGvA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-BMw_JMhz.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-BMw_JMhz#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b24jitss8o", "Integrity": "U0GckHJhkkN7/haQkrox4qg9+0T14+AqVX+4XpzQUbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-BMw_JMhz.js", "FileLength": 180965, "LastWriteTime": "2025-06-25T06:48:51.6783368+00:00"}, "9+BOV7BTa5hvpT3mGFmC4L5PR6JaYnCXp3WWQzmIwso=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CJChw8ry.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-CJChw8ry#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3hojn27uz5", "Integrity": "WU7To5P36WSPrBTns6F7IodNUsgkaYVYwM6l0+roFCs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-CJChw8ry.js", "FileLength": 182241, "LastWriteTime": "2025-06-25T07:12:12.7150374+00:00"}, "bXFR1r6gfMrA7YoF78l4mMn+plUAyWGYEKgVOnKebOA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CpdsusLQ.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-CpdsusLQ#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t1hsu86971", "Integrity": "rxzQtBXsGFOIIgNOn5P/0/nqaWJlhGZZRIv3XAQQYvA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-CpdsusLQ.css", "FileLength": 3109, "LastWriteTime": "2025-06-25T07:12:12.7150374+00:00"}, "W82oTJhmNgmqRqDKgG25sfb/VwYj/4MQ5uZRKpnVc/k=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-H3RB1j-F.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-H3RB1j-F#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b510bptf2a", "Integrity": "z4SYcgn8EAE0H0ZuaL9cB0oRVsMClNnvpG/pRwWTu/E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-H3RB1j-F.css", "FileLength": 1499, "LastWriteTime": "2025-06-25T06:48:51.6783368+00:00"}, "L9Y+YmKvnIPT3Oo6C3C9lA9apruradpA3qis5iZX7NM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-CYeuBsGv.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-CYeuBsGv#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "13f82m0q0o", "Integrity": "Xdg1mBYPnPVIKPbKyU2CmT4h2qPCtYHaKN5DxJB9C1Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-CYeuBsGv.js", "FileLength": 188791, "LastWriteTime": "2025-06-26T07:36:00.7794719+00:00"}, "h/bDKwo7BkPfDY48VLsuCG2+HO2L5Vssssx/tKforMY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-QbAIgLC0.js", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-QbAIgLC0#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ikfqtwtpkd", "Integrity": "Up7PWhNF/T4FcuflSln4/UcaBkCpxrQY+3CuOPe31aM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-QbAIgLC0.js", "FileLength": 186307, "LastWriteTime": "2025-06-25T08:02:53.1346386+00:00"}, "Yotrz+1uFXTHyh5b1+2boAWTMq6GxVG32onQxX1v9pI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-COJ0l26b.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-COJ0l26b#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3fgy8k75fl", "Integrity": "nw+epXvOTgghSpPVbCwitwYBHK4hmzB3j+HcYAdl+0I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-COJ0l26b.css", "FileLength": 4666, "LastWriteTime": "2025-06-25T08:02:53.1336391+00:00"}, "tZNlYuXVGTSH/JLJHogZoh4Fqb7YnUZVz9I7dnQoolc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\assets\\index-D678EY3y.css", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "assets/index-D678EY3y#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cuuedufbnm", "Integrity": "XFcrihttvxnhkGrzK/HEKcMgy8DRv1OsAVikSP7KtTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\index-D678EY3y.css", "FileLength": 9025, "LastWriteTime": "2025-06-26T07:36:00.7794719+00:00"}, "sckqxnt+QAtLuuFJPkF4aD08mGiINiajx0RkOitaHuc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\index.html", "SourceId": "VehicleActivatorAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\Activator\\VehicleActivatorAPI\\wwwroot\\", "BasePath": "_content/VehicleActivatorAPI", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "g9k842lu6v", "Integrity": "bEsESAXEkW2EAMh5xNfF/BWk+yi5i9XP6ma3i0xkGUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 464, "LastWriteTime": "2025-06-26T07:36:00.780472+00:00"}}, "CachedCopyCandidates": {}}