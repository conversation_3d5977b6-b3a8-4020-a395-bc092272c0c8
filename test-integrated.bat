@echo off
echo Testing Vehicle Activator System (Integrated Deployment)...

echo.
echo ========================================
echo Building and Starting Integrated Application...
echo ========================================
call run-integrated.bat

echo.
echo ========================================
echo Testing Integrated Deployment...
echo ========================================

echo Waiting for application to start...
timeout /t 10 /nobreak > nul

echo.
echo Testing Frontend (React App)...
curl -k -X GET "https://localhost:7000/" -H "accept: text/html" --max-time 10

echo.
echo.
echo Testing API Endpoints...
curl -k -X GET "https://localhost:7000/api/vehicles" -H "accept: application/json" --max-time 10

echo.
echo.
echo Testing Swagger UI...
curl -k -X GET "https://localhost:7000/swagger" -H "accept: text/html" --max-time 10

echo.
echo.
echo ========================================
echo Integrated Deployment Test Complete
echo ========================================
echo.
echo If all tests passed, the integrated deployment is working correctly:
echo - Frontend: https://localhost:7000/
echo - API: https://localhost:7000/api/
echo - Swagger: https://localhost:7000/swagger
echo.
echo Both frontend and backend are served from the same URL!
echo.
echo Press any key to exit...
pause > nul
