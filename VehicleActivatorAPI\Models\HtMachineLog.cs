using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VehicleActivatorAPI.Models
{
    [Table("HT_MachineLogs")]
    public class HtMachineLog
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        
        [Required]
        [Column(TypeName = "nvarchar(100)")]
        public string MachineId { get; set; } = string.Empty;
    }
}
