using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VehicleActivatorAPI.Models
{
    [Table("Temp_HoSoXeHoanTat")]
    public class TempHoSoXeHoanTat
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        
        [Column(TypeName = "nchar(50)")]
        public string? KhoaBaoGia { get; set; }
        
        [Column(TypeName = "nvarchar(150)")]
        public string? NguoiDuyet { get; set; }
        
        public DateTime? NgayDuyet { get; set; }
    }
}
