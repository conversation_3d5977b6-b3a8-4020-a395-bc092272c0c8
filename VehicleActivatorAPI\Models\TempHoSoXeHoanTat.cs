using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VehicleActivatorAPI.Models
{
    [Table("Temp_HoSoXeHoanTat")]
    public class TempHoSoXeHoanTat
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public string? KhoaBaoGia { get; set; }

        public string? NguoiDuyet { get; set; }

        // NgayDuyet is actually stored as DateTime in the database
        public DateTime? NgayDuyet { get; set; }
    }
}
