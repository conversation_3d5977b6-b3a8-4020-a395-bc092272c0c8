using Microsoft.EntityFrameworkCore;
using VehicleActivatorAPI.Models;

namespace VehicleActivatorAPI.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<ScBaoGia> ScBaoGia { get; set; }
        public DbSet<TempHoSoXeHoanTat> TempHoSoXeHoanTat { get; set; }
        public DbSet<HtMachineLog> HtMachineLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure ScBaoGia
            modelBuilder.Entity<ScBaoGia>(entity =>
            {
                entity.HasKey(e => e.Khoa);
                entity.ToTable("SC_BaoGia");
            });

            // Configure TempHoSoXeHoanTat
            modelBuilder.Entity<TempHoSoXeHoanTat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.ToTable("Temp_HoSoXeHoanTat");
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });
        }
    }
}
