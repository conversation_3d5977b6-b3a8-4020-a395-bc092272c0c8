using Microsoft.EntityFrameworkCore;
using VehicleActivatorAPI.Models;

namespace VehicleActivatorAPI.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<ScBaoGia> ScBaoGia { get; set; }
        public DbSet<TempHoSoXeHoanTat> TempHoSoXeHoanTat { get; set; }
        public DbSet<HtMachineLog> HtMachineLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure ScBaoGia
            modelBuilder.Entity<ScBaoGia>(entity =>
            {
                entity.HasKey(e => e.Khoa);
                entity.ToTable("SC_BaoGia");

                // Map NgayChungTuString to the actual database column NgayChungTu
                entity.Property(e => e.NgayChungTuString)
                    .HasColumnName("NgayChungTu")
                    .HasColumnType("nchar(8)");

                // Map decimal columns for SoKm fields
                entity.Property(e => e.SoKmHienTaiDecimal)
                    .HasColumnName("SoKmHienTai")
                    .HasColumnType("numeric(18,0)");

                entity.Property(e => e.SoKmTruocDecimal)
                    .HasColumnName("SoKmTruoc")
                    .HasColumnType("numeric(18,0)");

                // Ignore the computed properties
                entity.Ignore(e => e.NgayChungTu);
                entity.Ignore(e => e.SoKmHienTai);
                entity.Ignore(e => e.SoKmTruoc);
            });

            // Configure TempHoSoXeHoanTat
            modelBuilder.Entity<TempHoSoXeHoanTat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.ToTable("Temp_HoSoXeHoanTat");
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // NgayDuyet is stored as DateTime in the database
                entity.Property(e => e.NgayDuyet)
                    .HasColumnName("NgayDuyet");
            });
        }
    }
}
